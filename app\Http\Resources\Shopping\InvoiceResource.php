<?php

namespace App\Http\Resources\Shopping;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Shopping\InvoiceProductResource;
use App\Http\Resources\Shopping\TransactionResource;

/**
 * Resource class for transforming Invoice models into API responses.
 *
 * Provides a representation of an invoice with its ID, status, creation date, and transaction details.
 */
class InvoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Invoice ID
     * - Status (pending, paid, rejected)
     * - Creation date
     * - Products in the invoice
     * - Total price
     * - Address information (receiver name, phone, address, province, city, zip code)
     * - Transaction details
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (string) $this->_id,
            'status' => $this->status,
            'creation_date' => $this->created_at->format('Y-m-d H:i:s'),
            'products' => $this->whenLoaded('products', fn() => InvoiceProductResource::collection($this->products), []),
            'total' => $this->when(isset($this->total), $this->total),
            'total_discount' => $this->when(isset($this->total_discount) && $this->total_discount > 0, $this->total_discount),
            // Address information
            'address' => [
                'receiver_name' => $this->receiver_name,
                'receiver_phone' => $this->receiver_phone,
                'address' => $this->address,
                'province' => $this->province,
                'city' => $this->city,
                'zip_code' => $this->zip_code,
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
            ],
            // Transaction details
            'transactions' => $this->whenLoaded('transactions', fn() => TransactionResource::collection($this->transactions), []),
        ];
    }
}
